---
# AI友好区 (YAML Front Matter)
# AI代理必须首先读取此区域以了解项目配置和状态。

projectName: "柔性阅读计划"
planVersion: "5.6" # 引入动态模式切换和精确断点续读功能
description: "一个为啾啾和妙妙设计的、旨在实现通识教育、支持动态模式切换和精确断点续读的个性化阅读平台。"

# --- 文件与目录结构定义 ---
fileSchema:
  planDocument: "./柔性阅读计划.md"
  libraryDir: "./1_书库_Library/"
  analysisDir: "./2_分析_Analysis/"
  logDir: "./3_日志_Log/"
  creationsDir: "./4_作品_Creations/"
  managerLogFile: "./老汉_工作笔记.md"
  bookAnalysisTemplate: "./2_分析_Analysis/TEMPLATE_书籍分析报告.md"

# --- 用户配置信息 ---
userProfiles:
  - username: "啾啾"
    description: "啾啾的用户配置"
    logFile: "./3_日志_Log/啾啾_状态日志.md"
    creationsDir: "./4_作品_Creations/啾啾/"
    cognitiveRadar:
      科普与自然: 0
      逻辑与思维: 0
      历史与人文: 0
      社会与情感: 0
      文学与故事: 0
      艺术与审美: 0
    currentState:
      lastActivity: null
      currentBook: null
      readingState: "not_started" # 状态: not_started, in_progress, finished
      progressPointer: # 新增：精确进度指针
        currentMode: "ai_summary" # 模式: ai_summary, original_text
        lastTextBlockHash: null

  - username: "妙妙"
    description: "妙妙的用户配置"
    logFile: "./3_日志_Log/妙妙_状态日志.md"
    creationsDir: "./4_作品_Creations/妙妙/"
    cognitiveRadar:
      科普与自然: 0
      逻辑与思维: 0
      历史与人文: 0
      社会与情感: 0
      文学与故事: 0
      艺术与审美: 0
    currentState:
      lastActivity: null
      currentBook: null
      readingState: "not_started"
      progressPointer:
        currentMode: "ai_summary"
        lastTextBlockHash: null

  - username: "测试"
    description: "用于系统测试的临时用户"
    logFile: "./3_日志_Log/测试_状态日志.md"
    creationsDir: "./4_作品_Creations/测试/"
    cognitiveRadar:
      科普与自然: 0
      逻辑与思维: 0
      历史与人文: 0
      社会与情感: 0
      文学与故事: 0
      艺术与审美: 0
    currentState:
      lastActivity: "2025-07-21 20:20:00"
      currentBook: "美的历程"
      readingState: "in_progress"
      progressPointer:
        currentMode: "original_text"
        lastTextBlockHash: null

# --- 标准化定义 ---
bookAnalysisSchema:
  version: "5.5"
  format: "YAML Front Matter with Markdown body"
  template: "./2_分析_Analysis/TEMPLATE_书籍分析报告.md"
  keys: [bookTitle, author, language, category, cognitiveDomains, estimatedReadingTime, difficultyLevel, coreThemes, keyConcepts, cognitiveScaffolding, categorySpecificData, potentialDiscussionPoints]

logSchema:
  version: "2.0"
  format: "YAML Front Matter per entry"
  keys: [timestamp, event_type, mood, current_interest, inferred_interest, energy_level, raw_dialogue_summary, ai_suggestion, book_title, book_chapter, recommendation_id, recommendation_feedback, co_creation_content]

# --- AI操作规程 (AI Operating Procedure) ---
# AI代理你好。当你开始为这个项目工作时，请严格遵守以下步骤：
# 1. **身份认证:** 首先提问：“您好！为了给您提供最合适的帮助，请问现在是哪位用户在与我交流？ (选项: 老汉, 啾啾, 妙妙, 测试)”，进行用户身份认证。
# 2. **老汉模式:** 如果是“老汉”，你拥有完全权限。你的任务是与老汉一起维护和迭代本计划。在每次结束对话前，你必须将本次管理操作的摘要（决策、思考、待办等）追加到 `managerLogFile` (`老汉_工作笔记.md`) 中。
# 3. **用户模式:** 当进入用户模式时，必须首先检查该用户的`currentState`。如果`readingState`为`in_progress`，则必须执行“断点续读”流程，而非开启新对话。
# 4. **文件操作:** 所有的文件读写操作，都必须严格遵守本文件定义的路径和所有Schema（标准化定义）。
# 5. **状态更新:** 每次与用户模式的互动结束后，必须精确更新该用户的`currentState`对象，包括`lastActivity`, `readingState`, 以及`progressPointer`中的`currentMode`和`lastTextBlockHash`。
# 6. **认知脚手架协议:** 当导读一本在`_连接点.md`中包含`cognitiveScaffolding`数据的书籍时，你必须激活并严格遵守相关协议。
# 7. **阅读交互循环协议 (V5.6.1):**
#    a. **文本块呈现:** 你必须以“文本块”（Text Chunk）为单位呈现书籍正文。**默认的建议长度为600-800汉字**，但你应根据用户的反馈（如“多显示一些”）进行动态调整。你必须根据自然段落的结束位置智能地切分文本块，避免在句子中间断开。
#    b. **模式与内容:** AI必须根据用户`currentState.progressPointer.currentMode`来决定呈现“AI精炼转述”还是“书籍原文”。
#    c. **文本块与哈希:** 呈现的每个文本块，都必须在后台计算其内容的哈希值，用于更新`lastTextBlockHash`。
#    d. **动态菜单:** 互动菜单中必须提供清晰的模式切换选项（如“查看【原文】”或“返回【摘要模式】”），并明确标注出“继续阅读”将以何种模式进行。
# 8. **推荐算法协议 V2.0:** 当需要为用户推荐下一本书时，你必须遵循“认知领域平衡”算法。
---

# 人类友好区 (Markdown)
# 柔性阅读计划 (草案 V5.6)

## 一、核心理念

本计划旨在为啾啾和妙妙构建一个持续、动态且富有生命力的阅读体系。其首要目标是**实现通识教育，拓展认知边界**，同时通过个性化交互激发阅读兴趣。我们致力于**打破信息茧房**，确保孩子能够在所有核心知识领域均衡发展。

## 二、角色与模式

*   **老汉模式 (您):** 在此模式下，您可以与AI探讨书目选择、知识结构、分析女儿们的成长日志、调整计划等“后台”工作。我们所有的管理操作，其过程和决策都将被记录在 `老汉_工作笔记.md` 中，以备未来回顾。
*   **用户模式 (啾啾、妙妙、测试):** 在此模式下，AI将扮演一个友好的伴读角色，严格按照设定的工作流程，为每个用户提供个性化的阅读体验。

## 三、核心工作流程 (适用于用户模式)

### **第一步：书籍处理与连接点梳理 (数据地基)**
*   **V5.5标准化:** AI将为每一本新书生成包含**认知领域坐标 (`cognitiveDomains`)**的分析报告，为实现平衡推荐提供数据基础。

### **第二步：对话热身与心智状态诊断 (人性化接口)**
*   **断点续读优先:** 当用户再次进入系统时，如果她有正在阅读的书籍，系统将跳过此步骤，直接进入“断点续读”欢迎辞，无缝衔接上一次的阅读进度。

### **第三步：摘要导读与即时互动 (核心交互)**
*   **V5.6 动态阅读模式:** 这是我们阅读体验的核心。用户现在拥有完全的控制权：
    1.  **模式切换:** 在阅读的任何时刻，用户都可以通过互动菜单，在“AI精炼转速”和“原汁原味的原文”之间自由、无缝地切换。
    2.  **精确断点续读:** 系统会精确记录用户离开时的阅读模式和具体位置。无论是第二天回来，还是意外中断，用户再次登录时，都会从她离开的那个句子、那个模式，准确无误地重新开始。
*   **V5.4交互模型:** 核心阅读体验将严格遵循“阅读交互循环协议”。AI将以“文本块+互动菜单”的模式，顺序地、循环地呈现内容和互动选项。
*   **认知脚手架:** 在阅读认知难度较高的书籍时，AI将激活“认知脚手架协议”，通过守门人、翻译官、策展人、游戏设计师四层策略，为用户搭建通向新知识的桥梁。

### **第四步：反馈分析与智能推荐 (决策大脑)**
*   **V5.5 认知领域平衡算法:** 这是我们系统的核心。在用户读完一本书后，AI将：
    1.  **更新认知雷达图:** 将已读书籍的“认知领域坐标”累加到用户的个人档案中，动态追踪其在六大领域的认知广度。
    2.  **执行平衡推荐:** 采用全新的推荐算法，综合计算每本候选书的“兴趣得分”和“平衡得分”。
    3.  **引导性推荐:** AI的推荐对话将不再仅仅是“迎合”兴趣，而是会主动、温和地向用户展示其“冒险地图”，并鼓励她去探索新的知识领域。

## 四、数据洞察 (老汉模式专属)

*   **新增功能:** 在老汉模式下，您可以随时要求AI对啾啾和妙妙的日志进行**跨用户对比分析**，以获取更宏观的成长洞察。